package com.challanty.android.implode.ui.screens.leaderboard

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.challanty.android.implode.navigation.AppDestinations
import com.challanty.android.implode.ui.common.CommonScreenLayout
import com.challanty.android.implode.ui.common.Screen

@Composable
fun LeaderboardScreen(
    navController: NavController,
    viewModel: LeaderboardViewModel = hiltViewModel()
) {
    CommonScreenLayout(
        screenTitle = "Leaderboard",
        currentScreen = Screen.Leaderboard,
        onNavigate = { screen ->
            when (screen) {
                Screen.Game -> navController.navigate(AppDestinations.GAMEPLAY_ROUTE)
                Screen.Stats -> navController.navigate(AppDestinations.STATS_ROUTE)
                Screen.Settings -> navController.navigate(AppDestinations.SETTINGS_ROUTE)
                Screen.Help -> navController.navigate(AppDestinations.HELP_ROUTE)
                Screen.Leaderboard -> { /* Already on Leaderboard screen */ }
                Screen.About -> navController.navigate(AppDestinations.ABOUT_ROUTE)
                Screen.Loading -> { /* Handle Loading case if necessary */ }
            }
        },
        isScrollable = true
    ) {
        Text("Leaderboard Screen - Coming Soon!")
        Text("Global leaderboards will be available in a future update.")
    }
}
