package com.challanty.android.implode.ui.screens.stats

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.challanty.android.implode.data.repository.StatsRepository
import com.challanty.android.implode.datastore.PlayerStats
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the Stats Screen.
 * Manages player statistics display and operations.
 */
@HiltViewModel
class StatsViewModel @Inject constructor(
    private val statsRepository: StatsRepository
) : ViewModel() {

    /**
     * Player statistics state flow for UI consumption.
     */
    val playerStats: StateFlow<PlayerStatsUiState> = statsRepository.playerStatsFlow
        .map { stats ->
            PlayerStatsUiState(
                lastGameScore = stats.lastGameScore,
                highestGameScore = stats.highestGameScore,
                gamesPlayed = stats.gamesPlayed
            )
        }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = PlayerStatsUiState()
        )

    /**
     * Resets all player statistics to default values.
     */
    fun resetStats() {
        viewModelScope.launch {
            statsRepository.resetPlayerStats()
        }
    }

    /**
     * Updates the last game score and increments games played.
     * Also updates highest score if the new score is higher.
     */
    fun updateGameScore(score: Int) {
        viewModelScope.launch {
            statsRepository.updateGameScore(score)
        }
    }
}

/**
 * UI state for player statistics.
 */
data class PlayerStatsUiState(
    val lastGameScore: Int = 0,
    val highestGameScore: Int = 0,
    val gamesPlayed: Int = 0
) {
    /**
     * Calculates average score if games have been played.
     */
    val averageScore: Int
        get() = if (gamesPlayed > 0) {
            // Simple average based on highest score assumption
            // In a real implementation, you might want to track total score
            highestGameScore / gamesPlayed
        } else 0

    /**
     * Indicates if the player has played any games.
     */
    val hasPlayedGames: Boolean
        get() = gamesPlayed > 0

    /**
     * Indicates if the last game was the player's best game.
     */
    val lastGameWasBest: Boolean
        get() = lastGameScore == highestGameScore && highestGameScore > 0
}
