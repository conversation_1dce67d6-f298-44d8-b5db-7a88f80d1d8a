package com.challanty.android.implode.data.repository

import androidx.datastore.core.DataStore
import com.challanty.android.implode.datastore.GameSettings
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import java.io.IOException
import javax.inject.Inject

class GameRepository @Inject constructor(
    private val gameSettingsDataStore: DataStore<GameSettings>
) {
    val gameSettingsFlow: Flow<GameSettings> = gameSettingsDataStore.data
        .catch { exception ->
            // dataStore.data throws an IOException when an error is encountered when reading data
            if (exception is IOException) {
                emit(GameSettings.getDefaultInstance())
            } else {
                throw exception
            }
        }

    suspend fun updateHighScore(score: Int) {
        gameSettingsDataStore.updateData { currentSettings ->
            currentSettings.toBuilder().setHighScore(score).build()
        }
    }

    suspend fun setMusicEnabled(enabled: Boolean) {
        gameSettingsDataStore.updateData { currentSettings ->
            currentSettings.toBuilder().setMusicEnabled(enabled).build()
        }
    }

    suspend fun setSoundEffectsEnabled(enabled: Boolean) {
        gameSettingsDataStore.updateData { currentSettings ->
            currentSettings.toBuilder().setSoundEffectsEnabled(enabled).build()
        }
    }
}
