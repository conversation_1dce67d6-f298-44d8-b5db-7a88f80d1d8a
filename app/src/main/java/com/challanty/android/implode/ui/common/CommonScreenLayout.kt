package com.challanty.android.implode.ui.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

// Enum to represent the different screens for navigation
enum class Screen {
    Loading, Game, Stats, Settings, Help, Leaderboard, About
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CommonScreenLayout(
    screenTitle: String,
    currentScreen: Screen,
    onNavigate: (Screen) -> Unit,
    isScrollable: Boolean = true,
    titleBarOptions: @Composable RowScope.() -> Unit = {},
    content: @Composable ColumnScope.() -> Unit
) {
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(screenTitle) },
                actions = titleBarOptions,
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer,
                    titleContentColor = MaterialTheme.colorScheme.onPrimaryContainer
                )
            )
        },
        bottomBar = {
            BottomNavigationBar(currentScreen = currentScreen, onNavigate = onNavigate, enabled = currentScreen != Screen.Loading)
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            // Work Area
            Box(
                modifier = Modifier
                    .weight(1f) // Fills available space between title and nav
                    .fillMaxWidth()
                    .background(Color.LightGray) // Placeholder background
                    .then(
                        if (isScrollable) Modifier.verticalScroll(rememberScrollState())
                        else Modifier
                    )
                    .padding(16.dp), // Padding for content within the work area
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    content()
                }
            }
        }
    }
}

@Composable
fun BottomNavigationBar(currentScreen: Screen, onNavigate: (Screen) -> Unit, enabled: Boolean) {
    val items = listOf(
        Screen.Game,
        Screen.Settings,
        Screen.Help,
        Screen.Leaderboard,
        Screen.About
    )

    NavigationBar {
        items.forEach { screen ->
            NavigationBarItem(
                icon = { /* TODO: Add appropriate icons */ Text(screen.name.first().toString(), fontSize = 10.sp) },
                label = { Text(screen.name, fontSize = 10.sp) },
                selected = currentScreen == screen,
                onClick = { if (enabled && currentScreen != screen) onNavigate(screen) },
                enabled = enabled && currentScreen != screen
            )
        }
    }
}

// Example Usage (can be removed or moved to a preview file)
@Composable
fun PreviewCommonScreenLayout() {
    MaterialTheme { // Ensure a MaterialTheme is applied for previews
        CommonScreenLayout(
            screenTitle = "Game Screen",
            currentScreen = Screen.Game,
            onNavigate = { /* TODO: Handle navigation */ },
            isScrollable = false,
            titleBarOptions = {
                TextButton(onClick = { /*TODO*/ }) { Text("Options") }
            }
        ) {
            // Content for the Game Screen
            Text("Welcome to the Game!", style = MaterialTheme.typography.headlineMedium)
            Spacer(modifier = Modifier.height(16.dp))
            Button(onClick = { /* TODO */ }) {
                Text("Start Game")
            }
        }
    }
}
