package com.challanty.android.implode.ui.screens.about

import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.challanty.android.implode.navigation.AppDestinations
import com.challanty.android.implode.ui.common.CommonScreenLayout
import com.challanty.android.implode.ui.common.Screen

@Composable
fun AboutScreen(
    navController: NavController,
    viewModel: AboutViewModel = hiltViewModel()
) {
    CommonScreenLayout(
        screenTitle = "About",
        currentScreen = Screen.About,
        onNavigate = { screen ->
            when (screen) {
                Screen.Game -> navController.navigate(AppDestinations.GAMEPLAY_ROUTE)
                Screen.Stats -> navController.navigate(AppDestinations.STATS_ROUTE)
                Screen.Settings -> navController.navigate(AppDestinations.SETTINGS_ROUTE)
                Screen.Help -> navController.navigate(AppDestinations.HELP_ROUTE)
                Screen.Leaderboard -> navController.navigate(AppDestinations.LEADERBOARD_ROUTE)
                Screen.About -> { /* Already on About screen */ }
                Screen.Loading -> { /* Handle Loading case if necessary */ }
            }
        },
        isScrollable = true
    ) {
        // Content from original AboutScreen
        Text("About Screen Content") // Updated placeholder
        Button(onClick = { navController.navigate(AppDestinations.GAMEPLAY_ROUTE) }) {
            Text("Go to Gameplay")
        }
        // Add more about content here as needed (e.g., app version, developer info)
    }
}
