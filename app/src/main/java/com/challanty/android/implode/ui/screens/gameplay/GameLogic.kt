package com.challanty.android.implode.ui.screens.gameplay

import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class GameLogic @Inject constructor() {
    
    /**
     * Finds all connected squares of the same color starting from the given position.
     * Uses flood-fill algorithm to identify the cluster.
     */
    fun findCluster(
        board: GameBoard,
        startRow: Int,
        startCol: Int,
        targetColor: SquareColor
    ): Set<Pair<Int, Int>> {
        // Use ArrayDeque for better performance than mutableListOf for queue operations
        val queue = ArrayDeque<Pair<Int, Int>>()
        val visited = mutableSetOf<Pair<Int, Int>>()
        val cluster = mutableSetOf<Pair<Int, Int>>()

        queue.addLast(Pair(startRow, startCol))

        while (queue.isNotEmpty()) {
            val (row, col) = queue.removeFirst()

            if (visited.contains(Pair(row, col))) continue
            visited.add(Pair(row, col))

            if (row < 0 || row >= board.size || col < 0 || col >= board[0].size) continue

            val square = board[row][col]
            if (square.color != targetColor) continue

            cluster.add(Pair(row, col))

            // Add adjacent squares to queue
            queue.addLast(Pair(row - 1, col)) // Up
            queue.addLast(Pair(row + 1, col)) // Down
            queue.addLast(Pair(row, col - 1)) // Left
            queue.addLast(Pair(row, col + 1)) // Right
        }

        return cluster.toSet() // Return immutable set
    }
    
    /**
     * Validates if a move is possible at the given position.
     */
    fun isValidMove(board: GameBoard, row: Int, col: Int): Boolean {
        if (row < 0 || row >= board.size || col < 0 || col >= board[0].size) {
            return false
        }
        
        val square = board[row][col]
        if (square.color == null) {
            return false
        }
        
        val cluster = findCluster(board, row, col, square.color!!)
        return cluster.size >= 2
    }
    
    /**
     * Calculates optimal board dimensions based on available space.
     */
    fun calculateOptimalBoardSize(
        workAreaWidth: Int,
        workAreaHeight: Int,
        minSquareSize: Int = 40
    ): Pair<Int, Int> {
        val maxCols = (workAreaWidth / minSquareSize).coerceAtLeast(1)
        val maxRows = (workAreaHeight / minSquareSize).coerceAtLeast(1)
        
        // Prefer more square-like boards
        val aspectRatio = workAreaWidth.toFloat() / workAreaHeight.toFloat()
        
        return when {
            aspectRatio > 1.2f -> Pair(maxRows, maxCols) // Wider screen
            aspectRatio < 0.8f -> Pair(maxRows, maxCols) // Taller screen
            else -> Pair(maxRows, maxCols) // Square-ish screen
        }
    }
    
    /**
     * Checks if the game is complete (no valid moves remaining).
     */
    fun isGameComplete(board: GameBoard): Boolean {
        for (row in board.indices) {
            for (col in board[row].indices) {
                if (isValidMove(board, row, col)) {
                    return false
                }
            }
        }
        return true
    }
    
    /**
     * Counts remaining squares of each color on the board.
     */
    fun countRemainingSquares(board: GameBoard): Map<SquareColor, Int> {
        return BoardUtils.calculateInitialColorCounts(board)
    }
}
