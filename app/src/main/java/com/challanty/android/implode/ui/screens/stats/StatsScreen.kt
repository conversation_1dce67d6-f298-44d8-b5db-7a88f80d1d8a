package com.challanty.android.implode.ui.screens.stats

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.challanty.android.implode.navigation.AppDestinations
import com.challanty.android.implode.ui.common.CommonScreenLayout
import com.challanty.android.implode.ui.common.Screen

@Composable
fun StatsScreen(
    navController: NavController,
    viewModel: StatsViewModel = hiltViewModel()
) {
    val playerStats by viewModel.playerStats.collectAsState()

    CommonScreenLayout(
        screenTitle = "Player Statistics",
        currentScreen = Screen.Stats,
        onNavigate = { screen ->
            when (screen) {
                Screen.Game -> navController.navigate(AppDestinations.GAMEPLAY_ROUTE)
                Screen.Stats -> { /* Already on Stats screen */ }
                Screen.Settings -> navController.navigate(AppDestinations.SETTINGS_ROUTE)
                Screen.Help -> navController.navigate(AppDestinations.HELP_ROUTE)
                Screen.Leaderboard -> navController.navigate(AppDestinations.LEADERBOARD_ROUTE)
                Screen.About -> navController.navigate(AppDestinations.ABOUT_ROUTE)
                Screen.Loading -> { /* Handle Loading case if necessary */ }
            }
        },
        isScrollable = true
    ) {
        StatsContent(
            lastGameScore = playerStats.lastGameScore,
            highestGameScore = playerStats.highestGameScore,
            gamesPlayed = playerStats.gamesPlayed,
            onResetStats = { viewModel.resetStats() }
        )
    }
}

@Composable
fun StatsContent(
    lastGameScore: Int,
    highestGameScore: Int,
    gamesPlayed: Int,
    onResetStats: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        // Title
        Text(
            text = "Solo Game Statistics",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center
        )

        // Stats Cards
        Column(
            verticalArrangement = Arrangement.spacedBy(16.dp),
            modifier = Modifier.fillMaxWidth()
        ) {
            StatCard(
                title = "Last Game Score",
                value = lastGameScore.toString(),
                description = "Your most recent game score"
            )

            StatCard(
                title = "Highest Score",
                value = highestGameScore.toString(),
                description = "Your personal best score"
            )

            StatCard(
                title = "Games Played",
                value = gamesPlayed.toString(),
                description = "Total number of games completed"
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Additional Stats (calculated)
        if (gamesPlayed > 0) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.secondaryContainer
                ),
                shape = RoundedCornerShape(12.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Performance",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.SemiBold
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    val averageScore = if (gamesPlayed > 0) highestGameScore / gamesPlayed else 0
                    Text(
                        text = "Estimated Average: $averageScore",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSecondaryContainer
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Reset Stats Button
        OutlinedButton(
            onClick = onResetStats,
            modifier = Modifier.fillMaxWidth(0.6f),
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = MaterialTheme.colorScheme.error
            )
        ) {
            Text("Reset Statistics")
        }

        // Info Text
        Text(
            text = "Statistics are tracked locally for solo games only. " +
                    "Multiplayer and online leaderboards coming soon!",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(horizontal = 16.dp)
        )
    }
}

@Composable
fun StatCard(
    title: String,
    value: String,
    description: String
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        ),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = value,
                style = MaterialTheme.typography.headlineLarge,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary,
                fontSize = 32.sp
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Text(
                text = description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f),
                textAlign = TextAlign.Center
            )
        }
    }
}
