package com.challanty.android.implode.ui.screens.gameplay

import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.animateOffsetAsState
import androidx.compose.animation.core.tween
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.platform.LocalContext
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.min
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.challanty.android.implode.navigation.AppDestinations
import com.challanty.android.implode.ui.common.CommonScreenLayout
import com.challanty.android.implode.ui.common.Screen
import kotlinx.coroutines.delay

// Using screen-local constants for fast, responsive animations
internal const val SCREEN_SHRINK_ANIMATION_TIME_MS = 150L  // Reduced from 300ms to 150ms
internal const val SCREEN_SLIDE_ANIMATION_TIME_MS = 100L   // Reduced from 250ms to 100ms

// Animation specs defined locally, using screen-local constants
val squareAnimationSpec = tween<Float>(durationMillis = SCREEN_SHRINK_ANIMATION_TIME_MS.toInt())
val squareOffsetAnimationSpec = tween<androidx.compose.ui.unit.Dp>(durationMillis = SCREEN_SLIDE_ANIMATION_TIME_MS.toInt())

@Composable
fun GameplayScreen(
    navController: NavController,
    viewModel: GameplayViewModel = hiltViewModel()
) {
    val gameBoard by viewModel.gameBoard.collectAsState()
    val currentScore by viewModel.currentScore.collectAsState()
    val scoreAnimation by viewModel.scoreAnimation.collectAsState()
    val gameState by viewModel.gameState.collectAsState()
    val context = LocalContext.current

    // Initialize orientation manager through ViewModel
    LaunchedEffect(Unit) {
        viewModel.initializeOrientationManager(context)
    }

    // Clean up orientation manager when screen is disposed
    DisposableEffect(Unit) {
        onDispose {
            viewModel.cleanupOrientationManager()
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        CommonScreenLayout(
            screenTitle = "Game",
            currentScreen = Screen.Game,
            onNavigate = { screen ->
                when (screen) {
                    Screen.Stats -> navController.navigate(AppDestinations.STATS_ROUTE)
                    Screen.Settings -> navController.navigate(AppDestinations.SETTINGS_ROUTE)
                    Screen.Help -> navController.navigate(AppDestinations.HELP_ROUTE)
                    Screen.Leaderboard -> navController.navigate(AppDestinations.LEADERBOARD_ROUTE)
                    Screen.About -> navController.navigate(AppDestinations.ABOUT_ROUTE)
                    Screen.Game -> { /* Already on Game screen */ }
                    Screen.Loading -> { /* Handle Loading case if necessary */ }
                }
            },
            isScrollable = false,
            titleBarOptions = {
                ScoreDisplay(score = currentScore)
            }
        ) {
            GameBoardView(
                gameBoard = gameBoard,
                scoreAnimation = null, // Disabled for cluster removal observation
                onSquareClicked = { row, col ->
                    viewModel.onSquareClicked(row, col)
                },
                onBoardDimensionsCalculated = { rows, cols ->
                    viewModel.updateBoardSize(androidx.compose.ui.unit.IntSize(cols * 40, rows * 40))
                },
                onScoreAnimationComplete = {
                    // No-op since animation is disabled
                },
                modifier = Modifier.fillMaxSize()
            )
        }

        // Game Over overlay
        if (gameState.animationState is AnimationState.GameOver) {
            GameOverOverlay(
                finalScore = currentScore,
                onNewGame = { viewModel.startNewGame() },
                onMainMenu = { navController.popBackStack() }
            )
        }
    }
}

@Composable
fun GameBoardView(
    gameBoard: List<List<SquareState>>,
    scoreAnimation: ScoreAnimation?,
    onSquareClicked: (row: Int, col: Int) -> Unit,
    onBoardDimensionsCalculated: (rows: Int, cols: Int) -> Unit,
    onScoreAnimationComplete: () -> Unit,
    modifier: Modifier = Modifier
) {
    BoxWithConstraints(modifier = modifier.padding(8.dp)) {
        val availableWidthDp = maxWidth
        val availableHeightDp = maxHeight

        if (availableWidthDp.value <= 0 || availableHeightDp.value <= 0) return@BoxWithConstraints

        val workAreaAspectRatio = availableWidthDp.value / availableHeightDp.value
        val minSquareSize = 36.dp

        val maxColsPossible = (availableWidthDp / minSquareSize).toInt().coerceAtLeast(1)
        val maxRowsPossible = (availableHeightDp / minSquareSize).toInt().coerceAtLeast(1)

        val optimalCols: Int
        val optimalRows: Int

        if (workAreaAspectRatio >= 1.0f) { // Wider or square area
            optimalCols = maxColsPossible
            optimalRows = (optimalCols / workAreaAspectRatio).toInt().coerceAtLeast(1).coerceAtMost(maxRowsPossible)
        } else { // Taller area
            optimalRows = maxRowsPossible
            optimalCols = (optimalRows * workAreaAspectRatio).toInt().coerceAtLeast(1).coerceAtMost(maxColsPossible)
        }

        LaunchedEffect(optimalRows, optimalCols) {
            if (optimalRows > 0 && optimalCols > 0) {
                onBoardDimensionsCalculated(optimalRows, optimalCols)
            }
        }

        if (gameBoard.isEmpty() || optimalRows == 0 || optimalCols == 0) {
            Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                // CircularProgressIndicator() // Or some other placeholder
            }
            return@BoxWithConstraints
        }

        val squareSizeW = availableWidthDp / optimalCols
        val squareSizeH = availableHeightDp / optimalRows
        val finalSquareSize = min(squareSizeW, squareSizeH) // Uses androidx.compose.ui.unit.min for Dp

        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                modifier = Modifier.size(finalSquareSize * optimalCols, finalSquareSize * optimalRows),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                val currentBoardRows = gameBoard.size
                val currentBoardCols = gameBoard.getOrNull(0)?.size ?: 0

                repeat(kotlin.math.min(optimalRows, currentBoardRows)) { rowIndex -> // Corrected: kotlin.math.min for Ints
                    Row(
                        horizontalArrangement = Arrangement.Center
                    ) {
                        repeat(kotlin.math.min(optimalCols, currentBoardCols)) { colIndex -> // Corrected: kotlin.math.min for Ints
                            if (rowIndex < currentBoardRows && colIndex < gameBoard[rowIndex].size) {
                                val square = gameBoard[rowIndex][colIndex]
                                SquareView(
                                    square = square,
                                    size = finalSquareSize,
                                    onClick = {
                                        onSquareClicked(square.row, square.col)
                                    }
                                )
                            } else {
                                Box(Modifier.size(finalSquareSize).padding(1.dp).background(Color.LightGray.copy(alpha=0.1f)))
                            }
                        }
                    }
                }
            }
        }

        // Score animation overlay
        scoreAnimation?.let { animation ->
            ScoreAnimationOverlay(
                animation = animation,
                squareSize = finalSquareSize,
                boardRows = optimalRows,
                boardCols = optimalCols,
                onAnimationComplete = onScoreAnimationComplete
            )
        }
    }
}

@Composable
fun SquareView(
    square: SquareState,
    size: androidx.compose.ui.unit.Dp,
    onClick: () -> Unit
) {
    val density = LocalDensity.current

    val animatedScale by animateFloatAsState(
        targetValue = square.targetScale,
        animationSpec = squareAnimationSpec,
        label = "scale_${square.id}"
    )
    val animatedAlpha by animateFloatAsState(
        targetValue = square.targetAlpha,
        animationSpec = squareAnimationSpec,
        label = "alpha_${square.id}"
    )
    val animatedOffsetX by animateDpAsState(
        targetValue = square.targetOffsetX,
        animationSpec = squareOffsetAnimationSpec,
        label = "offsetX_${square.id}"
    )
    val animatedOffsetY by animateDpAsState(
        targetValue = square.targetOffsetY,
        animationSpec = squareOffsetAnimationSpec,
        label = "offsetY_${square.id}"
    )

    // Explicitly convert Dp to Px for graphicsLayer and define type
    val translationXPx: Float = with(density) { animatedOffsetX.toPx() }
    val translationYPx: Float = with(density) { animatedOffsetY.toPx() }

    Box(
        modifier = Modifier
            .size(size) 
            .graphicsLayer(
                scaleX = animatedScale,
                scaleY = animatedScale,
                alpha = animatedAlpha,
                translationX = translationXPx, 
                translationY = translationYPx  
            )
            .clickable(enabled = square.visualState == SquareVisualState.Normal && square.color != null, onClick = onClick)
    ) {
        if (square.visualState != SquareVisualState.Gone && square.color != null) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(1.dp) 
                    .background(square.color.colorVal)
            )
        }
    }
}

@Preview(showBackground = true, name = "Square Animation Preview")
@Composable
fun SquareViewAnimationPreview() {
    val squareState = remember { mutableStateOf(SquareState(row = 0, col = 0, color = SquareColor.Red, targetRow = 0, targetCol = 0)) }

    LaunchedEffect(Unit) {
        delay(1000)
        squareState.value = squareState.value.copy(targetScale = 0f, targetAlpha = 0f, visualState = SquareVisualState.Shrinking)
        delay(SCREEN_SHRINK_ANIMATION_TIME_MS + 500) // Using screen-local constant

        squareState.value = squareState.value.copy(
            targetScale = 1f, targetAlpha = 1f, 
            targetOffsetX = 50.dp, 
            visualState = SquareVisualState.Sliding
        )
        delay(SCREEN_SLIDE_ANIMATION_TIME_MS + 500) // Using screen-local constant

        squareState.value = squareState.value.copy(
            offsetX = 50.dp, 
            targetOffsetX = 0.dp, 
            visualState = SquareVisualState.Normal 
        )
        delay(SCREEN_SLIDE_ANIMATION_TIME_MS + 500) // Using screen-local constant

        squareState.value = squareState.value.copy(
            targetOffsetY = (-50).dp,
            visualState = SquareVisualState.Sliding
        )
        delay(SCREEN_SLIDE_ANIMATION_TIME_MS + 500) // Using screen-local constant

        squareState.value = squareState.value.copy(
            offsetY = (-50).dp,
            targetOffsetY = 0.dp,
            visualState = SquareVisualState.Normal
        )
    }

    Box(modifier = Modifier.fillMaxSize().padding(16.dp), contentAlignment = Alignment.Center) {
        SquareView(
            square = squareState.value,
            size = 50.dp, 
            onClick = { 
                val current = squareState.value
                squareState.value = current.copy(targetScale = if (current.targetScale == 1f) 0.5f else 1f)
            }
        )
    }
}

@Composable
fun ScoreDisplay(score: Int) {
    var previousScore by remember { mutableIntStateOf(score) }
    var shouldPulse by remember { mutableStateOf(false) }

    // Trigger pulse animation when score changes
    LaunchedEffect(score) {
        if (score > previousScore) {
            shouldPulse = true
            delay(300) // Pulse duration
            shouldPulse = false
        }
        previousScore = score
    }

    val pulseScale by animateFloatAsState(
        targetValue = if (shouldPulse) 1.2f else 1f,
        animationSpec = tween(durationMillis = 150),
        label = "score_pulse"
    )

    Text(
        text = "Score: $score",
        style = MaterialTheme.typography.titleMedium,
        color = MaterialTheme.colorScheme.onPrimaryContainer,
        modifier = Modifier.scale(pulseScale)
    )
}

@Composable
fun ScoreAnimationOverlay(
    animation: ScoreAnimation,
    squareSize: androidx.compose.ui.unit.Dp,
    boardRows: Int,
    boardCols: Int,
    onAnimationComplete: () -> Unit
) {
    var animationPhase by remember { mutableIntStateOf(0) }

    // Animation phases: 0 = show at gravity well, 1 = zoom to title bar, 2 = complete
    LaunchedEffect(animation) {
        animationPhase = 0
        delay(800) // Show at gravity well for 0.8 seconds (reduced from 2 seconds)
        animationPhase = 1
        delay(600) // Zoom animation duration (reduced from 1.5 seconds)
        animationPhase = 2
        onAnimationComplete()
    }

    if (animationPhase < 2) {
        // Calculate positions
        val gravityWellX = animation.gravityWellCol * squareSize.value
        val gravityWellY = animation.gravityWellRow * squareSize.value

        // Title bar position - move toward top-right where score is displayed
        val boardWidth = boardCols * squareSize.value
        val titleBarX = boardWidth * 0.8f // Move toward right side where score typically is
        val titleBarY = -squareSize.value * 2 // Move up toward title bar area

        // Animate position from gravity well to title bar
        val targetOffset = if (animationPhase == 0) {
            Offset(gravityWellX, gravityWellY)
        } else {
            Offset(titleBarX, titleBarY)
        }

        val animatedOffset by animateOffsetAsState(
            targetValue = targetOffset,
            animationSpec = tween(durationMillis = 600),  // Reduced from 1500ms to 600ms
            label = "score_position"
        )

        // Animate scale - start normal, then shrink as it moves to title bar
        val targetScale = if (animationPhase == 0) 1f else 0.3f
        val animatedScale by animateFloatAsState(
            targetValue = targetScale,
            animationSpec = tween(durationMillis = 600),  // Reduced from 1500ms to 600ms
            label = "score_scale"
        )

        // Animate alpha - fade out as it reaches title bar
        val targetAlpha = if (animationPhase == 0) 1f else 0f
        val animatedAlpha by animateFloatAsState(
            targetValue = targetAlpha,
            animationSpec = tween(durationMillis = 600, delayMillis = 400),  // Reduced from 1500ms/1000ms to 600ms/400ms
            label = "score_alpha"
        )

        Box(
            modifier = Modifier
                .offset(x = animatedOffset.x.dp, y = animatedOffset.y.dp)
                .scale(animatedScale)
                .alpha(animatedAlpha)
                .wrapContentSize()
        ) {
            ScorePopup(
                totalPoints = animation.totalPoints,
                baseScore = animation.baseScore,
                timeBonus = animation.timeBonus,
                allColorBonus = animation.allColorBonus,
                allColorBonusText = animation.allColorBonusText,
                isCompact = animationPhase == 1 // Show compact version when moving to title bar
            )
        }
    }
}

@Composable
fun GameOverOverlay(
    finalScore: Int,
    onNewGame: () -> Unit,
    onMainMenu: () -> Unit
) {
    var isVisible by remember { mutableStateOf(false) }

    // Show the overlay with a slight delay for dramatic effect
    LaunchedEffect(Unit) {
        delay(500) // Brief delay before showing "Game Over!"
        isVisible = true
    }

    if (isVisible) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black.copy(alpha = 0.7f))
                .clickable { /* Prevent clicks from passing through */ },
            contentAlignment = Alignment.Center
        ) {
            Card(
                modifier = Modifier
                    .padding(32.dp)
                    .wrapContentSize(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                ),
                shape = RoundedCornerShape(16.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 12.dp)
            ) {
                Column(
                    modifier = Modifier.padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // Game Over title
                    Text(
                        text = "Game Over!",
                        style = MaterialTheme.typography.headlineLarge,
                        color = MaterialTheme.colorScheme.primary,
                        textAlign = TextAlign.Center
                    )

                    // Final score
                    Text(
                        text = "Final Score: $finalScore",
                        style = MaterialTheme.typography.titleLarge,
                        color = MaterialTheme.colorScheme.onSurface,
                        textAlign = TextAlign.Center
                    )

                    // Action buttons
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        Button(
                            onClick = onNewGame,
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.primary
                            )
                        ) {
                            Text("New Game")
                        }

                        OutlinedButton(
                            onClick = onMainMenu
                        ) {
                            Text("Main Menu")
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun ScorePopup(
    totalPoints: Int,
    baseScore: Int,
    timeBonus: Int,
    allColorBonus: Int,
    allColorBonusText: String,
    isCompact: Boolean = false
) {
    Card(
        modifier = Modifier.wrapContentSize(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        ),
        shape = RoundedCornerShape(8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        if (isCompact) {
            // Compact version for animation to title bar
            Box(
                modifier = Modifier.padding(6.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "+$totalPoints",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        } else {
            // Full detailed version for gravity well display
            Column(
                modifier = Modifier.padding(12.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "+$totalPoints",
                    style = MaterialTheme.typography.headlineMedium,
                    color = MaterialTheme.colorScheme.primary
                )

                Text(
                    text = "Base: $baseScore",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )

                if (timeBonus > 0) {
                    Text(
                        text = "Speed: +$timeBonus",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.secondary
                    )
                }

                if (allColorBonus > 0) {
                    Text(
                        text = allColorBonusText,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.tertiary
                    )
                    Text(
                        text = "+$allColorBonus",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.tertiary
                    )
                }
            }
        }
    }
}
