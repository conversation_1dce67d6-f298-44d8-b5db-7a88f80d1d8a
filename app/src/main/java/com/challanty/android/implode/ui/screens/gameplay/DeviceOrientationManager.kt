package com.challanty.android.implode.ui.screens.gameplay

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.abs
import kotlin.math.atan2
import kotlin.math.sqrt

/**
 * Manages device orientation detection for the game.
 * 
 * According to AGENT.md:
 * - The game does not visually rotate the screen when device orientation changes
 * - Only the vertical and horizontal directions rotate with the device
 * - The app looks at device orientation at the time a gravity well is defined
 * - Reorienting the device while a hole is being filled does not change movement directions
 */
@Singleton
class DeviceOrientationManager @Inject constructor() : SensorEventListener {
    
    private var sensorManager: SensorManager? = null
    private var accelerometer: Sensor? = null
    private var isListening = false
    
    private val _currentOrientation = MutableStateFlow(DeviceOrientation.PORTRAIT)
    val currentOrientation: StateFlow<DeviceOrientation> = _currentOrientation.asStateFlow()
    
    // Smoothing for sensor readings
    private val alpha = 0.8f
    private var gravity = FloatArray(3)
    
    /**
     * Represents the four possible device orientations.
     * Each orientation defines how movement directions are mapped.
     */
    enum class DeviceOrientation(val rotationDegrees: Int) {
        PORTRAIT(0),           // Normal orientation
        LANDSCAPE_LEFT(90),    // Rotated 90° counter-clockwise
        PORTRAIT_UPSIDE_DOWN(180), // Rotated 180°
        LANDSCAPE_RIGHT(270);  // Rotated 90° clockwise (270° counter-clockwise)
        
        /**
         * Maps logical directions to actual board directions based on device orientation.
         * 
         * @param logicalDirection The intended movement direction (UP, DOWN, LEFT, RIGHT)
         * @return The actual board direction considering device rotation
         */
        fun mapDirection(logicalDirection: MovementDirection): MovementDirection {
            return when (this) {
                PORTRAIT -> logicalDirection
                LANDSCAPE_LEFT -> when (logicalDirection) {
                    MovementDirection.UP -> MovementDirection.LEFT
                    MovementDirection.DOWN -> MovementDirection.RIGHT
                    MovementDirection.LEFT -> MovementDirection.DOWN
                    MovementDirection.RIGHT -> MovementDirection.UP
                }
                PORTRAIT_UPSIDE_DOWN -> when (logicalDirection) {
                    MovementDirection.UP -> MovementDirection.DOWN
                    MovementDirection.DOWN -> MovementDirection.UP
                    MovementDirection.LEFT -> MovementDirection.RIGHT
                    MovementDirection.RIGHT -> MovementDirection.LEFT
                }
                LANDSCAPE_RIGHT -> when (logicalDirection) {
                    MovementDirection.UP -> MovementDirection.RIGHT
                    MovementDirection.DOWN -> MovementDirection.LEFT
                    MovementDirection.LEFT -> MovementDirection.UP
                    MovementDirection.RIGHT -> MovementDirection.DOWN
                }
            }
        }
    }
    
    /**
     * Represents the four movement directions on the game board.
     */
    enum class MovementDirection {
        UP, DOWN, LEFT, RIGHT
    }
    
    /**
     * Initializes the orientation manager with the given context.
     */
    fun initialize(context: Context) {
        sensorManager = context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
        accelerometer = sensorManager?.getDefaultSensor(Sensor.TYPE_ACCELEROMETER)
        println("DeviceOrientationManager: Initialized - SensorManager: ${sensorManager != null}, Accelerometer: ${accelerometer != null}")
    }
    
    /**
     * Starts listening for orientation changes.
     */
    fun startListening() {
        if (!isListening && accelerometer != null) {
            val success = sensorManager?.registerListener(this, accelerometer, SensorManager.SENSOR_DELAY_UI) ?: false
            isListening = success
            println("DeviceOrientationManager: Started listening - Success: $success")
        } else {
            println("DeviceOrientationManager: Cannot start listening - Already listening: $isListening, Accelerometer available: ${accelerometer != null}")
        }
    }
    
    /**
     * Stops listening for orientation changes.
     */
    fun stopListening() {
        if (isListening) {
            sensorManager?.unregisterListener(this)
            isListening = false
        }
    }
    
    override fun onSensorChanged(event: SensorEvent?) {
        if (event?.sensor?.type == Sensor.TYPE_ACCELEROMETER) {
            // Apply low-pass filter to smooth sensor readings
            gravity[0] = alpha * gravity[0] + (1 - alpha) * event.values[0]
            gravity[1] = alpha * gravity[1] + (1 - alpha) * event.values[1]
            gravity[2] = alpha * gravity[2] + (1 - alpha) * event.values[2]

            // Calculate orientation based on gravity vector
            val orientation = calculateOrientation(gravity[0], gravity[1])
            if (orientation != _currentOrientation.value) {
                println("DeviceOrientationManager: Orientation changed from ${_currentOrientation.value} to $orientation")
                _currentOrientation.value = orientation
            }
        }
    }
    
    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
        // Not needed for this implementation
    }
    
    /**
     * Calculates device orientation based on accelerometer readings.
     */
    private fun calculateOrientation(x: Float, y: Float): DeviceOrientation {
        val magnitude = sqrt(x * x + y * y)
        if (magnitude < 2.0f) {
            // Device is likely flat, keep current orientation
            return _currentOrientation.value
        }
        
        val angle = atan2(-x, y) * 180 / Math.PI
        
        return when {
            angle >= -45 && angle < 45 -> DeviceOrientation.PORTRAIT
            angle >= 45 && angle < 135 -> DeviceOrientation.LANDSCAPE_LEFT
            angle >= 135 || angle < -135 -> DeviceOrientation.PORTRAIT_UPSIDE_DOWN
            else -> DeviceOrientation.LANDSCAPE_RIGHT
        }
    }
    
    /**
     * Gets the current device orientation.
     * This is called when a gravity well is created to capture the orientation for movement.
     */
    fun getCurrentOrientation(): DeviceOrientation {
        return _currentOrientation.value
    }
}
