package com.challanty.android.implode.di

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.dataStore
import com.challanty.android.implode.data.datastore.GameSettingsSerializer
import com.challanty.android.implode.datastore.GameSettings
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

private const val DATA_STORE_FILE_NAME = "game_settings.pb"

private val Context.gameSettingsDataStore: DataStore<GameSettings> by dataStore(
    fileName = DATA_STORE_FILE_NAME,
    serializer = GameSettingsSerializer
)

@Module
@InstallIn(SingletonComponent::class)
object DataModule {

    @Provides
    @Singleton
    fun provideGameSettingsDataStore(@ApplicationContext appContext: Context): DataStore<GameSettings> {
        return appContext.gameSettingsDataStore
    }
}
