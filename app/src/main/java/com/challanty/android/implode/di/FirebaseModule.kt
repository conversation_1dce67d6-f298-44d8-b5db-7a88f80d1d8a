package com.challanty.android.implode.di

import com.challanty.android.implode.data.firebase.FirebaseService
import com.challanty.android.implode.data.firebase.FirebaseServiceImpl
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class FirebaseModule {

    @Binds
    abstract fun bindFirebaseService(
        firebaseServiceImpl: FirebaseServiceImpl
    ): FirebaseService
}
