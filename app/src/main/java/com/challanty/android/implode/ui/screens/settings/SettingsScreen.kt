package com.challanty.android.implode.ui.screens.settings

import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.challanty.android.implode.navigation.AppDestinations
import com.challanty.android.implode.ui.common.CommonScreenLayout
import com.challanty.android.implode.ui.common.Screen

@Composable
fun SettingsScreen(
    navController: NavController,
    viewModel: SettingsViewModel = hiltViewModel()
) {
    CommonScreenLayout(
        screenTitle = "Settings",
        currentScreen = Screen.Settings,
        onNavigate = { screen ->
            when (screen) {
                Screen.Game -> navController.navigate(AppDestinations.GAMEPLAY_ROUTE)
                Screen.Stats -> navController.navigate(AppDestinations.STATS_ROUTE)
                Screen.Settings -> { /* Already on Settings screen */ }
                Screen.Help -> navController.navigate(AppDestinations.HELP_ROUTE)
                Screen.Leaderboard -> navController.navigate(AppDestinations.LEADERBOARD_ROUTE)
                Screen.About -> navController.navigate(AppDestinations.ABOUT_ROUTE)
                Screen.Loading -> { /* Handle Loading case if necessary */ }
            }
        },
        isScrollable = true
    ) {
        // Placeholder for settings content
        Text("Settings Screen Content")
        // TODO: Add actual settings UI elements here (e.g., switches, sliders)
    }
}
