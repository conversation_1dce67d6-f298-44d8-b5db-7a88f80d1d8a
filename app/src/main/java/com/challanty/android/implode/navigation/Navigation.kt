package com.challanty.android.implode.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.challanty.android.implode.ui.screens.about.AboutScreen
import com.challanty.android.implode.ui.screens.gameplay.GameplayScreen
import com.challanty.android.implode.ui.screens.help.HelpScreen
import com.challanty.android.implode.ui.screens.leaderboard.LeaderboardScreen
import com.challanty.android.implode.ui.screens.settings.SettingsScreen
import com.challanty.android.implode.ui.screens.stats.StatsScreen

object AppDestinations {
    const val GAMEPLAY_ROUTE = "gameplay"
    const val STATS_ROUTE = "stats"
    const val HELP_ROUTE = "help"
    const val ABOUT_ROUTE = "about"
    const val SETTINGS_ROUTE = "settings"
    const val LEADERBOARD_ROUTE = "leaderboard"
}

@Composable
fun AppNavigation() {
    val navController = rememberNavController()
    NavHost(navController = navController, startDestination = AppDestinations.GAMEPLAY_ROUTE) {
        composable(AppDestinations.GAMEPLAY_ROUTE) {
            GameplayScreen(navController = navController)
        }
        composable(AppDestinations.STATS_ROUTE) {
            StatsScreen(navController = navController)
        }
        composable(AppDestinations.HELP_ROUTE) {
            HelpScreen(navController = navController)
        }
        composable(AppDestinations.ABOUT_ROUTE) {
            AboutScreen(navController = navController)
        }
        composable(AppDestinations.SETTINGS_ROUTE) {
            SettingsScreen(navController = navController)
        }
        composable(AppDestinations.LEADERBOARD_ROUTE) {
            LeaderboardScreen(navController = navController)
        }
    }
}
