package com.challanty.android.implode.data.repository

import androidx.datastore.core.DataStore
import com.challanty.android.implode.datastore.GameSettings
import com.challanty.android.implode.datastore.PlayerStats
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing player statistics.
 * Handles all data operations related to player stats using Protobuf DataStore.
 */
@Singleton
class StatsRepository @Inject constructor(
    private val gameSettingsDataStore: DataStore<GameSettings>
) {

    /**
     * Flow of player statistics.
     */
    val playerStatsFlow: Flow<PlayerStats> = gameSettingsDataStore.data
        .map { gameSettings ->
            gameSettings.playerStats ?: PlayerStats.getDefaultInstance()
        }
        .catch { exception ->
            if (exception is IOException) {
                emit(PlayerStats.getDefaultInstance())
            } else {
                throw exception
            }
        }

    /**
     * Updates the player's game score after completing a game.
     * This will:
     * - Set the last game score
     * - Update highest score if the new score is higher
     * - Increment games played count
     */
    suspend fun updateGameScore(score: Int) {
        gameSettingsDataStore.updateData { currentSettings ->
            val currentStats = currentSettings.playerStats ?: PlayerStats.getDefaultInstance()
            
            val updatedStats = currentStats.toBuilder()
                .setLastGameScore(score)
                .setHighestGameScore(maxOf(score, currentStats.highestGameScore))
                .setGamesPlayed(currentStats.gamesPlayed + 1)
                .build()

            currentSettings.toBuilder()
                .setPlayerStats(updatedStats)
                .build()
        }
    }

    /**
     * Updates only the last game score without incrementing games played.
     * Useful for updating score during gameplay.
     */
    suspend fun updateLastGameScore(score: Int) {
        gameSettingsDataStore.updateData { currentSettings ->
            val currentStats = currentSettings.playerStats ?: PlayerStats.getDefaultInstance()
            
            val updatedStats = currentStats.toBuilder()
                .setLastGameScore(score)
                .build()

            currentSettings.toBuilder()
                .setPlayerStats(updatedStats)
                .build()
        }
    }

    /**
     * Updates the highest score if the provided score is higher than current.
     */
    suspend fun updateHighestScore(score: Int) {
        gameSettingsDataStore.updateData { currentSettings ->
            val currentStats = currentSettings.playerStats ?: PlayerStats.getDefaultInstance()
            
            if (score > currentStats.highestGameScore) {
                val updatedStats = currentStats.toBuilder()
                    .setHighestGameScore(score)
                    .build()

                currentSettings.toBuilder()
                    .setPlayerStats(updatedStats)
                    .build()
            } else {
                currentSettings
            }
        }
    }

    /**
     * Increments the games played counter.
     */
    suspend fun incrementGamesPlayed() {
        gameSettingsDataStore.updateData { currentSettings ->
            val currentStats = currentSettings.playerStats ?: PlayerStats.getDefaultInstance()
            
            val updatedStats = currentStats.toBuilder()
                .setGamesPlayed(currentStats.gamesPlayed + 1)
                .build()

            currentSettings.toBuilder()
                .setPlayerStats(updatedStats)
                .build()
        }
    }

    /**
     * Resets all player statistics to default values.
     */
    suspend fun resetPlayerStats() {
        gameSettingsDataStore.updateData { currentSettings ->
            val resetStats = PlayerStats.newBuilder()
                .setLastGameScore(0)
                .setHighestGameScore(0)
                .setGamesPlayed(0)
                .build()

            currentSettings.toBuilder()
                .setPlayerStats(resetStats)
                .build()
        }
    }

    /**
     * Gets the current player statistics.
     */
    suspend fun getCurrentPlayerStats(): PlayerStats {
        return gameSettingsDataStore.data
            .map { it.playerStats ?: PlayerStats.getDefaultInstance() }
            .catch { emit(PlayerStats.getDefaultInstance()) }
            .first()
    }
}
