package com.challanty.android.implode.ui.screens.help

import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.challanty.android.implode.navigation.AppDestinations
import com.challanty.android.implode.ui.common.CommonScreenLayout
import com.challanty.android.implode.ui.common.Screen // Added import

@Composable
fun HelpScreen(
    navController: NavController,
    viewModel: HelpViewModel = hiltViewModel()
) {
    CommonScreenLayout(
        screenTitle = "Help",
        currentScreen = Screen.Help,
        onNavigate = { screen ->
            // Basic navigation mapping, can be improved later
            when (screen) {
                Screen.Game -> navController.navigate(AppDestinations.GAMEPLAY_ROUTE)
                Screen.Stats -> navController.navigate(AppDestinations.STATS_ROUTE)
                Screen.Settings -> navController.navigate(AppDestinations.SETTINGS_ROUTE)
                Screen.Help -> { /* Already on Help screen */ }
                Screen.Leaderboard -> navController.navigate(AppDestinations.LEADERBOARD_ROUTE)
                Screen.About -> navController.navigate(AppDestinations.ABOUT_ROUTE)
                Screen.Loading -> { /* Handle Loading case if necessary */ }
            }
        },
        isScrollable = true 
    ) {
        // Content from original HelpScreen
        Text("Help Screen Content") // Updated placeholder
        Button(onClick = { navController.navigate(AppDestinations.GAMEPLAY_ROUTE) }) {
            Text("Go to Gameplay")
        }
        // Add more help content here as needed
    }
}
