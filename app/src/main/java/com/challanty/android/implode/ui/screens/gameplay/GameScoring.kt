package com.challanty.android.implode.ui.screens.gameplay

import javax.inject.Inject
import javax.inject.Singleton

/**
 * Handles all game scoring logic following AGENT.md specifications.
 * 
 * Scoring Formula:
 * - Base Score: (cluster_size - 2) * 10
 * - Time Bonus: max(0, 30 - seconds_since_last_move) * 2
 * - All-Color Bonus: 100 + (n-1) * 50 where n is the number of colors completely cleared
 * 
 * This class is stateless and thread-safe.
 */
@Singleton
class GameScoring @Inject constructor() {
    
    companion object {
        private const val BASE_SCORE_MULTIPLIER = 10
        private const val BASE_CLUSTER_SIZE = 2
        private const val TIME_BONUS_MULTIPLIER = 2
        private const val MAX_TIME_BONUS_SECONDS = 30
        private const val ALL_COLOR_BASE_BONUS = 100
        private const val ALL_COLOR_INCREMENTAL_BONUS = 50
    }
    
    /**
     * Represents the current scoring state.
     * This is a lightweight data class for passing scoring information.
     */
    data class ScoringState(
        val currentScore: Int = 0,
        val lastMoveTime: Long = System.currentTimeMillis(),
        val allColorRegionsCleared: Int = 0,
        val initialColorCounts: Map<SquareColor, Int> = emptyMap()
    ) {
        fun withScore(newScore: Int): ScoringState = copy(currentScore = newScore)
        fun withLastMoveTime(time: Long): ScoringState = copy(lastMoveTime = time)
        fun withAllColorRegionsCleared(count: Int): ScoringState = copy(allColorRegionsCleared = count)
        fun withInitialColorCounts(counts: Map<SquareColor, Int>): ScoringState = copy(initialColorCounts = counts)
    }
    
    /**
     * Represents the result of a scoring calculation.
     */
    data class ScoreResult(
        val baseScore: Int,
        val timeBonus: Int,
        val allColorBonus: Int,
        val allColorBonusText: String,
        val totalScore: Int
    ) {
        val hasTimeBonus: Boolean get() = timeBonus > 0
        val hasAllColorBonus: Boolean get() = allColorBonus > 0
    }
    
    /**
     * Calculates the score for a move and returns both the result and updated state.
     * This is the main entry point for scoring calculations.
     */
    fun calculateMoveScore(
        clusterSize: Int,
        clusterColor: SquareColor,
        scoringState: ScoringState
    ): Pair<ScoreResult, ScoringState> {
        val currentTime = System.currentTimeMillis()
        
        // Calculate base score
        val baseScore = calculateBaseScore(clusterSize)
        
        // Calculate time bonus
        val timeBonus = calculateTimeBonus(currentTime, scoringState.lastMoveTime)
        
        // Check for all-color bonus
        val (allColorBonus, updatedState) = checkAllColorBonus(clusterSize, clusterColor, scoringState)
        val allColorBonusText = if (allColorBonus > 0) {
            "All ${clusterColor.name} cleared! +$allColorBonus"
        } else ""
        
        // Calculate total score
        val totalScore = baseScore + timeBonus + allColorBonus
        
        val scoreResult = ScoreResult(
            baseScore = baseScore,
            timeBonus = timeBonus,
            allColorBonus = allColorBonus,
            allColorBonusText = allColorBonusText,
            totalScore = totalScore
        )
        
        val finalState = updatedState.withLastMoveTime(currentTime)
        
        println("Score calculation: base=$baseScore, time=$timeBonus, allColor=$allColorBonus, total=$totalScore")
        
        return Pair(scoreResult, finalState)
    }
    
    /**
     * Calculates base score: (cluster_size - 2) * 10
     */
    private fun calculateBaseScore(clusterSize: Int): Int {
        return (clusterSize - BASE_CLUSTER_SIZE) * BASE_SCORE_MULTIPLIER
    }
    
    /**
     * Calculates time bonus: max(0, 30 - seconds_since_last_move) * 2
     */
    private fun calculateTimeBonus(currentTime: Long, lastMoveTime: Long): Int {
        val secondsSinceLastMove = (currentTime - lastMoveTime) / 1000
        val bonusSeconds = (MAX_TIME_BONUS_SECONDS - secondsSinceLastMove).coerceAtLeast(0)
        return (bonusSeconds * TIME_BONUS_MULTIPLIER).toInt()
    }
    
    /**
     * Checks for all-color bonus: 100 + (n-1) * 50
     */
    private fun checkAllColorBonus(
        clusterSize: Int,
        clusterColor: SquareColor,
        scoringState: ScoringState
    ): Pair<Int, ScoringState> {
        val initialCount = scoringState.initialColorCounts[clusterColor] ?: 0
        
        // Check if this cluster clears all squares of this color
        if (clusterSize == initialCount) {
            val newAllColorRegionsCleared = scoringState.allColorRegionsCleared + 1
            val bonus = ALL_COLOR_BASE_BONUS + (newAllColorRegionsCleared - 1) * ALL_COLOR_INCREMENTAL_BONUS
            
            println("All-color bonus! Cleared all $clusterColor squares ($clusterSize). Bonus: $bonus")
            
            val updatedState = scoringState.withAllColorRegionsCleared(newAllColorRegionsCleared)
            return Pair(bonus, updatedState)
        }
        
        return Pair(0, scoringState)
    }
    
    /**
     * Calculates completion bonus for finishing the game.
     * This is called when all squares are cleared.
     */
    fun calculateCompletionBonus(
        remainingTime: Long,
        totalMoves: Int,
        allColorRegionsCleared: Int
    ): ScoreResult {
        // Completion bonus formula can be customized here
        val timeBonus = (remainingTime / 1000 * 5).toInt() // 5 points per second remaining
        val efficiencyBonus = maxOf(0, 1000 - totalMoves * 10) // Bonus for fewer moves
        val perfectBonus = if (allColorRegionsCleared >= 5) 500 else 0 // Bonus for clearing all colors
        
        val totalBonus = timeBonus + efficiencyBonus + perfectBonus
        
        return ScoreResult(
            baseScore = 0,
            timeBonus = timeBonus,
            allColorBonus = perfectBonus,
            allColorBonusText = if (perfectBonus > 0) "Perfect Game! +$perfectBonus" else "",
            totalScore = totalBonus
        )
    }
}
