package com.challanty.android.implode.di

import android.content.Context
import com.challanty.android.implode.data.ads.AdService
import com.challanty.android.implode.data.ads.GoogleAdService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object AdModule {

    @Provides
    @Singleton
    fun provideAdService(@ApplicationContext context: Context): AdService {
        return GoogleAdService(context)
    }
}
