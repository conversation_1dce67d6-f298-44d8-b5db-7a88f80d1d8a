package com.challanty.android.implode.data.ads

import android.content.Context
import javax.inject.Inject

/**
 * Disabled AdService implementation - AdMob functionality is disabled
 */
class GoogleAdService @Inject constructor(
    private val context: Context
) : AdService {

    init {
        // AdMob initialization disabled
        println("AdMob is disabled - no ads will be shown")
    }

    override fun showBannerAd() {
        // AdMob disabled - no banner ads
        println("Banner ad disabled (AdMob feature disabled)")
    }

    override fun showInterstitialAd() {
        // AdMob disabled - no interstitial ads
        println("Interstitial ad disabled (AdMob feature disabled)")
    }
}
