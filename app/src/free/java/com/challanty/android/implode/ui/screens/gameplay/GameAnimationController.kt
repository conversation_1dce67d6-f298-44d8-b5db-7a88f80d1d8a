package com.challanty.android.implode.ui.screens.gameplay

import kotlinx.coroutines.delay
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Handles game animations for cluster removal and hole filling.
 * This is the standard version shared by free and paid flavors.
 * 
 * Uses the hemisphere-based alternating algorithm from AGENT.md:
 * - Divides board into 4 hemispheres relative to gravity well
 * - Alternates between Vertical Phase (top/bottom) and Horizontal Phase (left/right)
 * - Continues until no movement occurs in either phase
 * - Movement directions are mapped based on device orientation
 */
@Singleton
class GameAnimationController @Inject constructor() {
    companion object {
        const val HOLE_FILLING_DELAY_MS = 100L  // Reduced from 800ms to 100ms
        const val CLUSTER_REMOVAL_SHRINK_DELAY_MS = 150L  // Reduced from 300ms to 150ms
    }

    /**
     * Animates the removal of a cluster of squares.
     */
    suspend fun animateClusterRemoval(
        clusterSquares: Set<Pair<Int, Int>>,
        gravityWellLocation: Pair<Int, Int>,
        board: MutableList<MutableList<SquareState>>,
        boardRows: Int,
        boardCols: Int,
        onBoardUpdate: (GameBoard) -> Unit
    ) {
        println("STANDARD: Starting cluster removal animation for ${clusterSquares.size} squares")
        
        // Remove squares from the cluster
        for ((row, col) in clusterSquares) {
            if (row in 0 until boardRows && col in 0 until boardCols) {
                board[row][col] = board[row][col].copy(color = null)
            }
        }
        
        // Update the board to show removed squares
        onBoardUpdate(BoardUtils.toImmutableBoard(board))
        
        // Small delay for visual effect
        delay(CLUSTER_REMOVAL_SHRINK_DELAY_MS)
        
        println("STANDARD: Cluster removal animation complete")
    }

    /**
     * Animates the hole filling process using hemisphere-based movement.
     */
    suspend fun animateHoleFilling(
        gravityWell: Pair<Int, Int>,
        board: MutableList<MutableList<SquareState>>,
        boardRows: Int,
        boardCols: Int,
        orientation: DeviceOrientationManager.DeviceOrientation,
        onBoardUpdate: (GameBoard) -> Unit
    ) {
        println("STANDARD: Starting hemisphere-based hole filling animation")
        println("Gravity well at: $gravityWell, Device orientation: $orientation")
        
        // Alternating loop: Vertical Phase → Horizontal Phase → repeat until no movement
        var phaseNumber = 1
        var verticalMovementOccurred = true
        var horizontalMovementOccurred = true
        
        while (verticalMovementOccurred || horizontalMovementOccurred) {
            // VERTICAL PHASE: Top hemisphere moves down, bottom hemisphere moves up (simultaneously)
            verticalMovementOccurred = executeVerticalPhase(
                gravityWell, board, boardRows, boardCols, orientation, onBoardUpdate, phaseNumber
            )
            
            phaseNumber++
            
            // HORIZONTAL PHASE: Left hemisphere moves right, right hemisphere moves left (simultaneously)
            horizontalMovementOccurred = executeHorizontalPhase(
                gravityWell, board, boardRows, boardCols, orientation, onBoardUpdate, phaseNumber
            )
            
            phaseNumber++
            
            println("STANDARD: Vertical movement: $verticalMovementOccurred, Horizontal movement: $horizontalMovementOccurred")
        }
        
        println("STANDARD: Hemisphere-based hole filling complete")
    }

    /**
     * Execute vertical phase: move top hemisphere down, bottom hemisphere up.
     */
    private suspend fun executeVerticalPhase(
        gravityWell: Pair<Int, Int>,
        board: MutableList<MutableList<SquareState>>,
        boardRows: Int,
        boardCols: Int,
        orientation: DeviceOrientationManager.DeviceOrientation,
        onBoardUpdate: (GameBoard) -> Unit,
        phaseNumber: Int
    ): Boolean {
        // Map logical directions based on device orientation
        val logicalDown = orientation.mapDirection(DeviceOrientationManager.MovementDirection.DOWN)
        val logicalUp = orientation.mapDirection(DeviceOrientationManager.MovementDirection.UP)
        
        return executePhase(
            gravityWell, board, boardRows, boardCols, orientation, onBoardUpdate,
            "Vertical", phaseNumber,
            logicalDown,  // Top hemisphere moves toward gravity (down in device orientation)
            logicalUp     // Bottom hemisphere moves toward gravity (up in device orientation)
        )
    }

    /**
     * Execute horizontal phase: move left hemisphere right, right hemisphere left.
     */
    private suspend fun executeHorizontalPhase(
        gravityWell: Pair<Int, Int>,
        board: MutableList<MutableList<SquareState>>,
        boardRows: Int,
        boardCols: Int,
        orientation: DeviceOrientationManager.DeviceOrientation,
        onBoardUpdate: (GameBoard) -> Unit,
        phaseNumber: Int
    ): Boolean {
        // Map logical directions based on device orientation
        val logicalRight = orientation.mapDirection(DeviceOrientationManager.MovementDirection.RIGHT)
        val logicalLeft = orientation.mapDirection(DeviceOrientationManager.MovementDirection.LEFT)
        
        return executePhase(
            gravityWell, board, boardRows, boardCols, orientation, onBoardUpdate,
            "Horizontal", phaseNumber,
            logicalRight, // Left hemisphere moves toward gravity (right in device orientation)
            logicalLeft   // Right hemisphere moves toward gravity (left in device orientation)
        )
    }

    /**
     * Execute a movement phase with two directions simultaneously.
     */
    private suspend fun executePhase(
        gravityWell: Pair<Int, Int>,
        board: MutableList<MutableList<SquareState>>,
        boardRows: Int,
        boardCols: Int,
        orientation: DeviceOrientationManager.DeviceOrientation,
        onBoardUpdate: (GameBoard) -> Unit,
        phaseName: String,
        phaseNumber: Int,
        direction1: DeviceOrientationManager.MovementDirection,
        direction2: DeviceOrientationManager.MovementDirection
    ): Boolean {
        println("STANDARD: Starting $phaseName Phase $phaseNumber")
        
        var movementOccurred = false
        var iterationCount = 0
        
        // Continue moving until no more movement in this phase
        do {
            iterationCount++
            var movementInIteration = false
            
            // Find squares in each hemisphere
            val squaresToMove1 = findSquaresInHemisphere(board, boardRows, boardCols, gravityWell, direction1)
            val squaresToMove2 = findSquaresInHemisphere(board, boardRows, boardCols, gravityWell, direction2)
            
            // Move squares in direction1 (e.g., top hemisphere moving down)
            if (squaresToMove1.isNotEmpty()) {
                for ((row, col) in squaresToMove1) {
                    val (newRow, newCol) = getNextPosition(row, col, direction1)
                    if (canMoveSquare(board, boardRows, boardCols, row, col, newRow, newCol, gravityWell, getStopOnePositionBeyond(direction1))) {
                        moveSquare(board, row, col, newRow, newCol)
                        movementInIteration = true
                    }
                }
            }
            
            // Move squares in direction2 (e.g., bottom hemisphere moving up)
            if (squaresToMove2.isNotEmpty()) {
                for ((row, col) in squaresToMove2) {
                    val (newRow, newCol) = getNextPosition(row, col, direction2)
                    if (canMoveSquare(board, boardRows, boardCols, row, col, newRow, newCol, gravityWell, getStopOnePositionBeyond(direction2))) {
                        moveSquare(board, row, col, newRow, newCol)
                        movementInIteration = true
                    }
                }
            }
            
            if (movementInIteration) {
                movementOccurred = true
                onBoardUpdate(BoardUtils.toImmutableBoard(board))
                delay(HOLE_FILLING_DELAY_MS)
            }
            
        } while (movementInIteration && iterationCount < 100) // Safety limit
        
        println("STANDARD: $phaseName Phase $phaseNumber complete after $iterationCount iterations")
        return movementOccurred
    }

    /**
     * Find squares in a hemisphere that should move in the given direction.
     */
    private fun findSquaresInHemisphere(
        board: MutableList<MutableList<SquareState>>,
        boardRows: Int,
        boardCols: Int,
        gravityWell: Pair<Int, Int>,
        direction: DeviceOrientationManager.MovementDirection
    ): List<Pair<Int, Int>> {
        val (wellRow, wellCol) = gravityWell
        val squaresToMove = mutableListOf<Pair<Int, Int>>()
        
        for (row in 0 until boardRows) {
            for (col in 0 until boardCols) {
                if (board[row][col].color != null && isInHemisphere(row, col, wellRow, wellCol, direction)) {
                    squaresToMove.add(Pair(row, col))
                }
            }
        }
        
        return squaresToMove
    }

    /**
     * Check if a position is in the hemisphere that moves in the given direction.
     */
    private fun isInHemisphere(
        row: Int, col: Int, wellRow: Int, wellCol: Int,
        direction: DeviceOrientationManager.MovementDirection
    ): Boolean {
        return when (direction) {
            DeviceOrientationManager.MovementDirection.DOWN -> row < wellRow  // Top hemisphere
            DeviceOrientationManager.MovementDirection.UP -> row > wellRow    // Bottom hemisphere
            DeviceOrientationManager.MovementDirection.RIGHT -> col < wellCol // Left hemisphere
            DeviceOrientationManager.MovementDirection.LEFT -> col > wellCol  // Right hemisphere
        }
    }

    /**
     * Get the next position when moving in a direction.
     */
    private fun getNextPosition(
        row: Int, col: Int,
        direction: DeviceOrientationManager.MovementDirection
    ): Pair<Int, Int> {
        return when (direction) {
            DeviceOrientationManager.MovementDirection.DOWN -> Pair(row + 1, col)
            DeviceOrientationManager.MovementDirection.UP -> Pair(row - 1, col)
            DeviceOrientationManager.MovementDirection.RIGHT -> Pair(row, col + 1)
            DeviceOrientationManager.MovementDirection.LEFT -> Pair(row, col - 1)
        }
    }

    /**
     * Check if a square can move to a new position.
     * Uses directional boundary checking like the experimental version.
     */
    private fun canMoveSquare(
        board: MutableList<MutableList<SquareState>>,
        boardRows: Int, boardCols: Int,
        fromRow: Int, fromCol: Int, toRow: Int, toCol: Int,
        gravityWell: Pair<Int, Int>,
        stopOnePositionBeyond: Boolean
    ): Boolean {
        // Check bounds
        if (toRow < 0 || toRow >= boardRows || toCol < 0 || toCol >= boardCols) return false

        // Can't move to occupied position
        if (board[toRow][toCol].color != null) return false

        // Check directional gravity well boundaries (includes gravity well position blocking)
        if (!isWithinGravityWellBounds(fromRow, fromCol, toRow, toCol, gravityWell, stopOnePositionBeyond)) {
            println("STANDARD: Blocking move from ($fromRow, $fromCol) to ($toRow, $toCol) - gravity well boundary")
            return false
        }

        return true
    }

    /**
     * Check if movement is within gravity well bounds based on direction.
     * All squares moving in the same direction should have uniform behavior.
     * Matches experimental version logic exactly.
     */
    private fun isWithinGravityWellBounds(
        fromRow: Int, fromCol: Int, toRow: Int, toCol: Int,
        gravityWell: Pair<Int, Int>, stopOnePositionBeyond: Boolean
    ): Boolean {
        val gravityRow = gravityWell.first
        val gravityCol = gravityWell.second

        // Determine movement direction
        val deltaRow = toRow - fromRow
        val deltaCol = toCol - fromCol

        // Apply directional boundaries exactly like experimental version
        when {
            deltaRow < 0 -> { // Moving up
                return if (stopOnePositionBeyond) {
                    toRow >= gravityRow - 1  // Stop at position immediately above gravity well
                } else {
                    toRow >= gravityRow      // Stop at gravity well row
                }
            }
            deltaRow > 0 -> { // Moving down
                return if (stopOnePositionBeyond) {
                    toRow <= gravityRow + 1  // Stop at position immediately below gravity well
                } else {
                    toRow <= gravityRow      // Stop at gravity well row
                }
            }
            deltaCol < 0 -> { // Moving left
                return if (stopOnePositionBeyond) {
                    toCol >= gravityCol - 1  // Stop at position immediately left of gravity well
                } else {
                    toCol >= gravityCol      // Stop at gravity well column
                }
            }
            deltaCol > 0 -> { // Moving right
                return if (stopOnePositionBeyond) {
                    toCol <= gravityCol + 1  // Stop at position immediately right of gravity well
                } else {
                    toCol <= gravityCol      // Stop at gravity well column
                }
            }
            else -> return true // No movement
        }
    }

    /**
     * Move a square from one position to another.
     */
    private fun moveSquare(
        board: MutableList<MutableList<SquareState>>,
        fromRow: Int, fromCol: Int, toRow: Int, toCol: Int
    ) {
        val square = board[fromRow][fromCol]
        board[fromRow][fromCol] = board[fromRow][fromCol].copy(color = null)
        board[toRow][toCol] = board[toRow][toCol].copy(color = square.color)
    }

    /**
     * Determine if movement should stop one position beyond gravity well.
     * Based on hemisphere physics: squares moving toward gravity well should get as close as possible.
     */
    private fun getStopOnePositionBeyond(direction: DeviceOrientationManager.MovementDirection): Boolean {
        return when (direction) {
            // UP and LEFT movements stop one position beyond to avoid collision
            DeviceOrientationManager.MovementDirection.UP -> true    // Bottom hemisphere moving up stops one position below well
            DeviceOrientationManager.MovementDirection.LEFT -> true  // Right hemisphere moving left stops one position right of well
            // DOWN and RIGHT movements can get adjacent to gravity well
            DeviceOrientationManager.MovementDirection.DOWN -> false // Top hemisphere moving down can get adjacent to well
            DeviceOrientationManager.MovementDirection.RIGHT -> false // Left hemisphere moving right can get adjacent to well
        }
    }
}
