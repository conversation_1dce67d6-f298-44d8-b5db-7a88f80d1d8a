{"project_info": {"project_number": "77655969774", "project_id": "implodegame", "storage_bucket": "implodegame.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:77655969774:android:d87a867eb20d26dcc948ff", "android_client_info": {"package_name": "com.challanty.android.implode"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyBS9gH8hCApXC7cxilzEA6Cyabu3f_MkUE"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:77655969774:android:94efa274b1bc14f6c948ff", "android_client_info": {"package_name": "com.challanty.android.implode.free"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyBS9gH8hCApXC7cxilzEA6Cyabu3f_MkUE"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:77655969774:android:102d44a8f5595d9bc948ff", "android_client_info": {"package_name": "com.challanty.android.implode.paid"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyBS9gH8hCApXC7cxilzEA6Cyabu3f_MkUE"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:77655969774:android:experimental123456789", "android_client_info": {"package_name": "com.challanty.android.implode.experimental"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyBS9gH8hCApXC7cxilzEA6Cyabu3f_MkUE"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}], "configuration_version": "1"}